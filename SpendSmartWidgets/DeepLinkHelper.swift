//
//  DeepLinkHelper.swift
//  SpendSmartWidgets
//
//  Created by SpendSmart Team on 2025-01-20.
//

import Foundation

// MARK: - Deep Link Types (Shared)
enum DeepLinkDestination {
    case addExpense
    case addExpenseWithCategory(String)
    case receiptDetail(String) // Receipt ID
    case dashboard
    case history
    case settings
}

// MARK: - Deep Link Helper (Widget-specific)
struct DeepLinkManager {
    // MARK: - URL Generation
    static func createURL(for destination: DeepLinkDestination) -> URL? {
        let baseURL = "spendsmart://"
        
        switch destination {
        case .addExpense:
            return URL(string: "\(baseURL)add-expense")
            
        case .addExpenseWithCategory(let category):
            let encodedCategory = category.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? category
            return URL(string: "\(baseURL)add-expense?category=\(encodedCategory)")
            
        case .receiptDetail(let receiptId):
            return URL(string: "\(baseURL)receipt/\(receiptId)")
            
        case .dashboard:
            return URL(string: "\(baseURL)dashboard")
            
        case .history:
            return URL(string: "\(baseURL)history")
            
        case .settings:
            return URL(string: "\(baseURL)settings")
        }
    }
}
