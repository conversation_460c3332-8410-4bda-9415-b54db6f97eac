//
//  WidgetDataService.swift
//  SpendSmartWidgets
//
//  Created by SpendSmart Team on 2025-01-20.
//

import Foundation
import WidgetKit

class WidgetDataService {
    static let shared = WidgetDataService()

    private init() {}

    // MARK: - Data Fetching

    func getReceipts() -> [Receipt] {
        // Use the same local storage service as the main app
        return LocalStorageService.shared.getReceipts()
    }

    func getCurrentMonthReceipts() -> [Receipt] {
        let allReceipts = getReceipts()
        let calendar = Calendar.current
        let now = Date()

        return allReceipts.filter { receipt in
            calendar.isDate(receipt.purchase_date, equalTo: now, toGranularity: .month)
        }
    }

    func getRecentReceipts(limit: Int = 5) -> [Receipt] {
        let allReceipts = getReceipts()
        return Array(allReceipts.sorted { $0.purchase_date > $1.purchase_date }.prefix(limit))
    }

    // MARK: - Summary Calculations

    func calculateCurrentMonthSummary() -> (totalExpense: Double, totalSavings: Double, receiptCount: Int) {
        let currentMonthReceipts = getCurrentMonthReceipts()
        let currencyManager = CurrencyManager.shared
        let preferredCurrency = currencyManager.preferredCurrency

        var totalExpense = 0.0
        var totalSavings = 0.0

        for receipt in currentMonthReceipts {
            // Convert amounts to preferred currency
            let convertedAmount = currencyManager.convertAmountSync(receipt.actualAmountSpent,
                                                                 from: receipt.currency,
                                                                 to: preferredCurrency)
            let convertedSavings = currencyManager.convertAmountSync(receipt.savings,
                                                                  from: receipt.currency,
                                                                  to: preferredCurrency)

            totalExpense += convertedAmount
            totalSavings += convertedSavings
        }

        return (totalExpense, totalSavings, currentMonthReceipts.count)
    }

    func getMostUsedCategories(limit: Int = 5) -> [String] {
        let allReceipts = getReceipts()
        var categoryCount: [String: Int] = [:]

        for receipt in allReceipts {
            for item in receipt.items {
                if !item.isDiscount {
                    categoryCount[item.category, default: 0] += 1
                }
            }
        }

        return Array(categoryCount.sorted { $0.value > $1.value }.prefix(limit).map { $0.key })
    }

    // MARK: - Budget Progress (Placeholder for future budget feature)

    func getBudgetProgress() -> Double {
        // For now, return a mock progress based on spending patterns
        // This will be replaced with actual budget data when budget feature is implemented
        let summary = calculateCurrentMonthSummary()

        // Mock budget of $2000 per month - this should come from user settings in the future
        let mockMonthlyBudget = 2000.0

        // Calculate progress as percentage of budget used
        return min(summary.totalExpense / mockMonthlyBudget, 1.0)
    }

    func getEstimatedMonthlyBudget() -> Double {
        // Calculate average monthly spending from last 3 months as estimated budget
        let calendar = Calendar.current
        let now = Date()
        let threeMonthsAgo = calendar.date(byAdding: .month, value: -3, to: now) ?? now

        let recentReceipts = getReceipts().filter { receipt in
            receipt.purchase_date >= threeMonthsAgo
        }

        if recentReceipts.isEmpty {
            return 2000.0 // Default budget
        }

        let currencyManager = CurrencyManager.shared
        let preferredCurrency = currencyManager.preferredCurrency

        var totalSpent = 0.0
        for receipt in recentReceipts {
            let convertedAmount = currencyManager.convertAmountSync(receipt.actualAmountSpent,
                                                                 from: receipt.currency,
                                                                 to: preferredCurrency)
            totalSpent += convertedAmount
        }

        // Calculate average monthly spending
        let monthsSpan = max(1, calendar.dateComponents([.month], from: threeMonthsAgo, to: now).month ?? 1)
        return totalSpent / Double(monthsSpan)
    }

    // MARK: - Chart Data

    func getMonthlySpendingData() -> [(month: String, amount: Double)] {
        let allReceipts = getReceipts()
        let calendar = Calendar.current
        let currencyManager = CurrencyManager.shared
        let preferredCurrency = currencyManager.preferredCurrency

        var monthlyTotals: [String: Double] = [:]
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "MMM"

        // Get last 6 months of data
        for i in 0..<6 {
            if let date = calendar.date(byAdding: .month, value: -i, to: Date()) {
                let monthStr = dateFormatter.string(from: date)
                monthlyTotals[monthStr] = 0.0
            }
        }

        for receipt in allReceipts {
            let monthStr = dateFormatter.string(from: receipt.purchase_date)
            if monthlyTotals.keys.contains(monthStr) {
                let convertedAmount = currencyManager.convertAmountSync(receipt.actualAmountSpent,
                                                                     from: receipt.currency,
                                                                     to: preferredCurrency)
                monthlyTotals[monthStr, default: 0] += convertedAmount
            }
        }

        return monthlyTotals.map { (month: $0.key, amount: $0.value) }
            .sorted { first, second in
                let formatter = DateFormatter()
                formatter.dateFormat = "MMM"
                let firstDate = formatter.date(from: first.month) ?? Date()
                let secondDate = formatter.date(from: second.month) ?? Date()
                return firstDate < secondDate
            }
    }
}
