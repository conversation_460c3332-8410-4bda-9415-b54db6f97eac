//
//  RecentReceiptsWidget.swift
//  SpendSmartWidgets
//
//  Created by SpendSmart Team on 2025-01-20.
//

import WidgetKit
import SwiftUI
import AppIntents
import UIKit

struct RecentReceiptsWidget: Widget {
    let kind: String = "RecentReceiptsWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: RecentReceiptsProvider()) { entry in
            RecentReceiptsWidgetEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
        }
        .configurationDisplayName("Recent Receipts")
        .description("View your most recent receipts at a glance.")
        .supportedFamilies([.systemMedium, .systemLarge])
    }
}

struct RecentReceiptsEntry: TimelineEntry {
    let date: Date
    let recentReceipts: [Receipt]
}

struct RecentReceiptsProvider: TimelineProvider {
    func placeholder(in context: Context) -> RecentReceiptsEntry {
        let placeholderReceipts = [
            Receipt(
                id: UUID(),
                user_id: UUID(),
                image_url: "",
                total_amount: 45.67,
                items: [],
                store_name: "Starbucks",
                store_address: "123 Main St",
                receipt_name: "Coffee & Pastry",
                purchase_date: Date(),
                currency: "USD",
                payment_method: "Credit Card",
                total_tax: 3.45
            ),
            Receipt(
                id: UUID(),
                user_id: UUID(),
                image_url: "",
                total_amount: 123.45,
                items: [],
                store_name: "Target",
                store_address: "456 Oak Ave",
                receipt_name: "Groceries",
                purchase_date: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date(),
                currency: "USD",
                payment_method: "Debit Card",
                total_tax: 9.87
            ),
            Receipt(
                id: UUID(),
                user_id: UUID(),
                image_url: "",
                total_amount: 78.90,
                items: [],
                store_name: "Shell",
                store_address: "789 Pine Rd",
                receipt_name: "Gas",
                purchase_date: Calendar.current.date(byAdding: .day, value: -2, to: Date()) ?? Date(),
                currency: "USD",
                payment_method: "Credit Card",
                total_tax: 5.67
            )
        ]

        return RecentReceiptsEntry(
            date: Date(),
            recentReceipts: placeholderReceipts
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (RecentReceiptsEntry) -> ()) {
        let entry = createEntry(for: context.family)
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        let entry = createEntry(for: context.family)

        // Update every 30 minutes
        let nextUpdate = Calendar.current.date(byAdding: .minute, value: 30, to: Date()) ?? Date()
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))

        completion(timeline)
    }

    private func createEntry(for family: WidgetFamily) -> RecentReceiptsEntry {
        let dataService = WidgetDataService.shared
        let limit = family == .systemMedium ? 3 : 5
        let recentReceipts = dataService.getRecentReceipts(limit: limit)

        return RecentReceiptsEntry(
            date: Date(),
            recentReceipts: recentReceipts
        )
    }
}

struct RecentReceiptsWidgetEntryView: View {
    var entry: RecentReceiptsProvider.Entry
    @Environment(\.widgetFamily) var family
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        switch family {
        case .systemMedium:
            MediumRecentReceiptsView(entry: entry)
        case .systemLarge:
            LargeRecentReceiptsView(entry: entry)
        default:
            MediumRecentReceiptsView(entry: entry)
        }
    }
}

// MARK: - Medium Widget View
struct MediumRecentReceiptsView: View {
    let entry: RecentReceiptsEntry
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Recent Receipts")
                        .font(.instrumentSans(size: 16, weight: .semibold))
                        .foregroundColor(colorScheme == .dark ? .white : .black)

                    Text("Last \(entry.recentReceipts.count) purchases")
                        .font(.instrumentSans(size: 12))
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "receipt")
                    .font(.system(size: 16))
                    .foregroundColor(.blue)
            }

            // Receipts List
            if entry.recentReceipts.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "receipt")
                        .font(.system(size: 24))
                        .foregroundColor(.secondary)

                    Text("No receipts yet")
                        .font(.instrumentSans(size: 14))
                        .foregroundColor(.secondary)

                    Text("Start scanning to see them here")
                        .font(.instrumentSans(size: 11))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                VStack(spacing: 8) {
                    ForEach(Array(entry.recentReceipts.prefix(3)), id: \.id) { receipt in
                        Button(intent: OpenReceiptIntent(receiptId: receipt.id.uuidString)) {
                            HStack(spacing: 12) {
                                // Store Icon
                                ZStack {
                                    Circle()
                                        .fill(Color.blue.opacity(0.1))
                                        .frame(width: 32, height: 32)

                                    Text(String(receipt.store_name.prefix(1)).uppercased())
                                        .font(.instrumentSans(size: 12, weight: .bold))
                                        .foregroundColor(.blue)
                                }

                                // Receipt Info
                                VStack(alignment: .leading, spacing: 2) {
                                    Text(receipt.store_name)
                                        .font(.instrumentSans(size: 12, weight: .medium))
                                        .foregroundColor(colorScheme == .dark ? .white : .black)
                                        .lineLimit(1)

                                    Text(formatRelativeDate(receipt.purchase_date))
                                        .font(.instrumentSans(size: 10))
                                        .foregroundColor(.secondary)
                                }

                                Spacer()

                                // Amount
                                Text(CurrencyManager.shared.formatAmount(receipt.total_amount, currencyCode: receipt.currency))
                                    .font(.spaceGrotesk(size: 12, weight: .bold))
                                    .foregroundColor(colorScheme == .dark ? .white : .black)
                                    .minimumScaleFactor(0.8)
                                    .lineLimit(1)
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 6)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(colorScheme == .dark ? Color.white.opacity(0.05) : Color.black.opacity(0.03))
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            }
        }
        .padding(16)
    }

    private func formatRelativeDate(_ date: Date) -> String {
        let calendar = Calendar.current
        let now = Date()

        if calendar.isDateInToday(date) {
            return "Today"
        } else if calendar.isDateInYesterday(date) {
            return "Yesterday"
        } else {
            let days = calendar.dateComponents([.day], from: date, to: now).day ?? 0
            if days < 7 {
                return "\(days)d ago"
            } else {
                let formatter = DateFormatter()
                formatter.dateFormat = "MMM d"
                return formatter.string(from: date)
            }
        }
    }
}

// MARK: - Large Widget View
struct LargeRecentReceiptsView: View {
    let entry: RecentReceiptsEntry
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        VStack(spacing: 16) {
            // Header with Summary
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Recent Receipts")
                        .font(.instrumentSans(size: 18, weight: .semibold))
                        .foregroundColor(colorScheme == .dark ? .white : .black)

                    if !entry.recentReceipts.isEmpty {
                        let totalAmount = entry.recentReceipts.reduce(0) { total, receipt in
                            total + CurrencyManager.shared.convertAmountSync(receipt.total_amount, from: receipt.currency, to: CurrencyManager.shared.preferredCurrency)
                        }

                        Text("Total: \(CurrencyManager.shared.formatAmount(totalAmount, currencyCode: CurrencyManager.shared.preferredCurrency))")
                            .font(.instrumentSans(size: 14))
                            .foregroundColor(.secondary)
                    } else {
                        Text("No recent purchases")
                            .font(.instrumentSans(size: 14))
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Image(systemName: "receipt")
                        .font(.system(size: 20))
                        .foregroundColor(.blue)

                    Text("\(entry.recentReceipts.count) receipts")
                        .font(.instrumentSans(size: 12))
                        .foregroundColor(.secondary)
                }
            }

            // Receipts List
            if entry.recentReceipts.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "receipt")
                        .font(.system(size: 32))
                        .foregroundColor(.secondary)

                    Text("No receipts yet")
                        .font(.instrumentSans(size: 16))
                        .foregroundColor(.secondary)

                    Text("Start scanning receipts to see them here")
                        .font(.instrumentSans(size: 13))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)

                    Button(intent: OpenAppToAddExpenseIntent(category: nil)) {
                        HStack {
                            Image(systemName: "camera.fill")
                                .font(.system(size: 12))
                            Text("Scan Receipt")
                                .font(.instrumentSans(size: 12, weight: .medium))
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.blue)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                ScrollView {
                    VStack(spacing: 10) {
                        ForEach(entry.recentReceipts, id: \.id) { receipt in
                            Button(intent: OpenReceiptIntent(receiptId: receipt.id.uuidString)) {
                                HStack(spacing: 12) {
                                    // Store Icon
                                    ZStack {
                                        Circle()
                                            .fill(Color.blue.opacity(0.1))
                                            .frame(width: 40, height: 40)

                                        Text(String(receipt.store_name.prefix(1)).uppercased())
                                            .font(.instrumentSans(size: 16, weight: .bold))
                                            .foregroundColor(.blue)
                                    }

                                    // Receipt Info
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text(receipt.store_name)
                                            .font(.instrumentSans(size: 14, weight: .medium))
                                            .foregroundColor(colorScheme == .dark ? .white : .black)
                                            .lineLimit(1)

                                        Text(receipt.receipt_name)
                                            .font(.instrumentSans(size: 12))
                                            .foregroundColor(.secondary)
                                            .lineLimit(1)

                                        Text(formatRelativeDate(receipt.purchase_date))
                                            .font(.instrumentSans(size: 11))
                                            .foregroundColor(.secondary)
                                    }

                                    Spacer()

                                    // Amount and Arrow
                                    VStack(alignment: .trailing, spacing: 4) {
                                        Text(CurrencyManager.shared.formatAmount(receipt.total_amount, currencyCode: receipt.currency))
                                            .font(.spaceGrotesk(size: 14, weight: .bold))
                                            .foregroundColor(colorScheme == .dark ? .white : .black)
                                            .minimumScaleFactor(0.8)
                                            .lineLimit(1)

                                        Image(systemName: "chevron.right")
                                            .font(.system(size: 10))
                                            .foregroundColor(.secondary)
                                    }
                                }
                                .padding(.horizontal, 12)
                                .padding(.vertical, 10)
                                .background(
                                    RoundedRectangle(cornerRadius: 10)
                                        .fill(colorScheme == .dark ? Color.white.opacity(0.05) : Color.black.opacity(0.03))
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }
            }
        }
        .padding(16)
    }

    private func formatRelativeDate(_ date: Date) -> String {
        let calendar = Calendar.current
        let now = Date()

        if calendar.isDateInToday(date) {
            let formatter = DateFormatter()
            formatter.timeStyle = .short
            return "Today, \(formatter.string(from: date))"
        } else if calendar.isDateInYesterday(date) {
            return "Yesterday"
        } else {
            let days = calendar.dateComponents([.day], from: date, to: now).day ?? 0
            if days < 7 {
                return "\(days) days ago"
            } else {
                let formatter = DateFormatter()
                formatter.dateFormat = "MMM d"
                return formatter.string(from: date)
            }
        }
    }
}

// MARK: - App Intent

struct OpenReceiptIntent: AppIntent {
    static var title: LocalizedStringResource = "Open Receipt"
    static var description = IntentDescription("Opens a specific receipt in SpendSmart.")

    @Parameter(title: "Receipt ID")
    var receiptId: String

    init() {
        self.receiptId = ""
    }

    init(receiptId: String) {
        self.receiptId = receiptId
    }

    func perform() async throws -> some IntentResult {
        // Widget extensions can't use UIApplication.shared
        // The system will automatically open the main app when this intent is triggered
        return .result()
    }
}
