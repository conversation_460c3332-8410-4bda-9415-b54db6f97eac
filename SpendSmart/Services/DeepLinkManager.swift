//
//  DeepLinkManager.swift
//  SpendSmart
//
//  Created by SpendSmart Team on 2025-01-20.
//

import Foundation
import SwiftUI

// MARK: - Deep Link Types
enum DeepLinkDestination {
    case addExpense
    case addExpenseWithCategory(String)
    case receiptDetail(String) // Receipt ID
    case dashboard
    case history
    case settings
}

// MARK: - Deep Link Manager
class DeepLinkManager: ObservableObject {
    static let shared = DeepLinkManager()

    @Published var pendingDestination: DeepLinkDestination?
    @Published var shouldNavigate = false

    private init() {}

    // MARK: - URL Handling
    func handleURL(_ url: URL) {
        print("🔗 Handling deep link: \(url.absoluteString)")

        guard url.scheme == "spendsmart" else {
            print("❌ Invalid URL scheme: \(url.scheme ?? "nil")")
            return
        }

        let host = url.host ?? ""
        let pathComponents = url.pathComponents.filter { $0 != "/" }
        let queryItems = URLComponents(url: url, resolvingAgainstBaseURL: false)?.queryItems

        switch host {
        case "add-expense":
            handleAddExpense(pathComponents: pathComponents, queryItems: queryItems)

        case "receipt":
            handleReceiptDetail(pathComponents: pathComponents)

        case "dashboard":
            navigateTo(.dashboard)

        case "history":
            navigateTo(.history)

        case "settings":
            navigateTo(.settings)

        default:
            print("❌ Unknown deep link host: \(host)")
        }
    }

    // MARK: - Navigation Handlers
    private func handleAddExpense(pathComponents: [String], queryItems: [URLQueryItem]?) {
        // Check for category parameter
        if let categoryItem = queryItems?.first(where: { $0.name == "category" }),
           let category = categoryItem.value {
            navigateTo(.addExpenseWithCategory(category))
        } else {
            navigateTo(.addExpense)
        }
    }

    private func handleReceiptDetail(pathComponents: [String]) {
        guard let receiptId = pathComponents.first else {
            print("❌ No receipt ID provided in deep link")
            return
        }

        navigateTo(.receiptDetail(receiptId))
    }

    private func navigateTo(_ destination: DeepLinkDestination) {
        DispatchQueue.main.async {
            self.pendingDestination = destination
            self.shouldNavigate = true
            print("✅ Deep link navigation set: \(destination)")
        }
    }

    // MARK: - Navigation Completion
    func navigationCompleted() {
        DispatchQueue.main.async {
            self.pendingDestination = nil
            self.shouldNavigate = false
        }
    }

    // MARK: - URL Generation (for testing)
    static func createURL(for destination: DeepLinkDestination) -> URL? {
        let baseURL = "spendsmart://"

        switch destination {
        case .addExpense:
            return URL(string: "\(baseURL)add-expense")

        case .addExpenseWithCategory(let category):
            let encodedCategory = category.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? category
            return URL(string: "\(baseURL)add-expense?category=\(encodedCategory)")

        case .receiptDetail(let receiptId):
            return URL(string: "\(baseURL)receipt/\(receiptId)")

        case .dashboard:
            return URL(string: "\(baseURL)dashboard")

        case .history:
            return URL(string: "\(baseURL)history")

        case .settings:
            return URL(string: "\(baseURL)settings")
        }
    }
}

// MARK: - Deep Link Navigation Helper
struct DeepLinkNavigationHandler: ViewModifier {
    @ObservedObject var deepLinkManager = DeepLinkManager.shared
    @EnvironmentObject var appState: AppState

    func body(content: Content) -> some View {
        content
            .onChange(of: deepLinkManager.shouldNavigate) { _, shouldNavigate in
                if shouldNavigate, let destination = deepLinkManager.pendingDestination {
                    handleNavigation(to: destination)
                }
            }
    }

    private func handleNavigation(to destination: DeepLinkDestination) {
        switch destination {
        case .addExpense:
            // Show new expense sheet
            appState.preselectedCategory = nil
            appState.showNewExpenseSheet = true
            print("📱 Opening new expense sheet")

        case .addExpenseWithCategory(let category):
            // Show new expense sheet with preselected category
            appState.preselectedCategory = category
            appState.showNewExpenseSheet = true
            print("📱 Opening new expense sheet with category: \(category)")

        case .receiptDetail(let receiptId):
            // Navigate to history tab and show specific receipt
            if let uuid = UUID(uuidString: receiptId) {
                appState.selectedTab = 1 // History tab
                appState.selectedReceiptId = uuid
                print("📱 Navigating to receipt: \(uuid)")
            }

        case .dashboard:
            // Navigate to dashboard
            appState.selectedTab = 0
            print("📱 Navigating to dashboard")

        case .history:
            // Navigate to history
            appState.selectedTab = 1
            print("📱 Navigating to history")

        case .settings:
            // Navigate to settings
            appState.selectedTab = 2
            print("📱 Navigating to settings")
        }

        // Mark navigation as completed
        deepLinkManager.navigationCompleted()
    }
}

// MARK: - View Extension
extension View {
    func handleDeepLinks() -> some View {
        self.modifier(DeepLinkNavigationHandler())
    }
}
