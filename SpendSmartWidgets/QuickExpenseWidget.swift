//
//  QuickExpenseWidget.swift
//  SpendSmartWidgets
//
//  Created by SpendSmart Team on 2025-01-20.
//

import WidgetKit
import SwiftUI
import AppIntents
import UIKit

struct QuickExpenseWidget: Widget {
    let kind: String = "QuickExpenseWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: QuickExpenseProvider()) { entry in
            QuickExpenseWidgetEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
        }
        .configurationDisplayName("Quick Expense Entry")
        .description("Quickly add expenses with common categories.")
        .supportedFamilies([.systemMedium])
    }
}

struct QuickExpenseEntry: TimelineEntry {
    let date: Date
    let mostUsedCategories: [String]
}

struct QuickExpenseProvider: TimelineProvider {
    func placeholder(in context: Context) -> QuickExpenseEntry {
        QuickExpenseEntry(
            date: Date(),
            mostUsedCategories: ["Food", "Transport", "Shopping", "Entertainment", "Bills"]
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (QuickExpenseEntry) -> ()) {
        let entry = createEntry()
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        let entry = createEntry()

        // Update every 6 hours
        let nextUpdate = Calendar.current.date(byAdding: .hour, value: 6, to: Date()) ?? Date()
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))

        completion(timeline)
    }

    private func createEntry() -> QuickExpenseEntry {
        let dataService = WidgetDataService.shared
        let categories = dataService.getMostUsedCategories(limit: 5)

        // Fallback categories if no data
        let fallbackCategories = ["Food", "Transport", "Shopping", "Entertainment", "Bills"]
        let finalCategories = categories.isEmpty ? fallbackCategories : categories

        return QuickExpenseEntry(
            date: Date(),
            mostUsedCategories: Array(finalCategories.prefix(5))
        )
    }
}

struct QuickExpenseWidgetEntryView: View {
    var entry: QuickExpenseProvider.Entry
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        VStack(spacing: 12) {
            headerSection
            quickAmountButtons
            categoryButtons
            mainAddButton
        }
        .padding(16)
    }

    // MARK: - View Components

    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text("Quick Expense")
                    .font(.instrumentSans(size: 16, weight: .semibold))
                    .foregroundColor(colorScheme == .dark ? .white : .black)

                Text("Tap to add expense")
                    .font(.instrumentSans(size: 12))
                    .foregroundColor(.secondary)
            }

            Spacer()

            Image(systemName: "plus.circle.fill")
                .font(.system(size: 20))
                .foregroundColor(.blue)
        }
    }

    private var quickAmountButtons: some View {
        HStack(spacing: 8) {
            ForEach([5, 10, 25, 50], id: \.self) { amount in
                quickAmountButton(for: amount)
            }
        }
    }

    private func quickAmountButton(for amount: Int) -> some View {
        Button(intent: AddQuickExpenseIntent(amount: Double(amount), category: "General")) {
            VStack(spacing: 2) {
                Text(CurrencyManager.shared.formatAmount(Double(amount), currencyCode: CurrencyManager.shared.preferredCurrency))
                    .font(.spaceGrotesk(size: 12, weight: .bold))
                    .foregroundColor(.blue)
                    .minimumScaleFactor(0.8)
                    .lineLimit(1)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 32)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.blue.opacity(0.1))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.blue.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var categoryButtons: some View {
        VStack(spacing: 6) {
            Text("Common Categories")
                .font(.instrumentSans(size: 11))
                .foregroundColor(.secondary)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 6), count: 3), spacing: 6) {
                ForEach(Array(entry.mostUsedCategories.prefix(6)), id: \.self) { category in
                    categoryButton(for: category)
                }
            }
        }
    }

    private func categoryButton(for category: String) -> some View {
        Button(intent: OpenAppToAddExpenseIntent(category: category)) {
            Text(category)
                .font(.instrumentSans(size: 10, weight: .medium))
                .foregroundColor(colorScheme == .dark ? .white : .black)
                .minimumScaleFactor(0.8)
                .lineLimit(1)
                .frame(maxWidth: .infinity)
                .frame(height: 24)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(colorScheme == .dark ? Color.white.opacity(0.1) : Color.black.opacity(0.05))
                )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var mainAddButton: some View {
        Button(intent: OpenAppToAddExpenseIntent(category: nil)) {
            HStack {
                Image(systemName: "camera.fill")
                    .font(.system(size: 12))
                Text("Scan Receipt")
                    .font(.instrumentSans(size: 12, weight: .medium))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 32)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.blue)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - App Intents

struct AddQuickExpenseIntent: AppIntent {
    static var title: LocalizedStringResource = "Add Quick Expense"
    static var description = IntentDescription("Quickly add an expense with a preset amount.")

    @Parameter(title: "Amount")
    var amount: Double

    @Parameter(title: "Category")
    var category: String

    init() {
        self.amount = 0.0
        self.category = ""
    }

    init(amount: Double, category: String) {
        self.amount = amount
        self.category = category
    }

    func perform() async throws -> some IntentResult {
        // Widget extensions can't use UIApplication.shared
        // The system will automatically open the main app when this intent is triggered
        return .result()
    }
}

struct OpenAppToAddExpenseIntent: AppIntent {
    static var title: LocalizedStringResource = "Open App to Add Expense"
    static var description = IntentDescription("Opens SpendSmart to add a new expense.")

    @Parameter(title: "Category")
    var category: String?

    init() {
        self.category = nil
    }

    init(category: String?) {
        self.category = category
    }

    func perform() async throws -> some IntentResult {
        // Widget extensions can't use UIApplication.shared
        // The system will automatically open the main app when this intent is triggered
        return .result()
    }
}
