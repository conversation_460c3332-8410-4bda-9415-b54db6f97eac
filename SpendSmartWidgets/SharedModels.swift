//
//  SharedModels.swift
//  SpendSmartWidgets
//
//  Created by SpendSmart Team on 2025-01-20.
//

import Foundation
import SwiftUI

// MARK: - Receipt Model (Shared with main app)
// This should be identical to the main app's Receipt model

struct Receipt: Identifiable, Codable, Equatable {
    var id: UUID
    var user_id: UUID
    var image_urls: [String]
    var total_amount: Double
    var items: [ReceiptItem]
    var store_name: String
    var store_address: String
    var receipt_name: String
    var purchase_date: Date
    var currency: String
    var payment_method: String
    var total_tax: Double
    var logo_search_term: String?

    var image_url: String {
        return image_urls.first ?? "placeholder_url"
    }

    var actualAmountSpent: Double {
        return total_amount
    }

    var savings: Double {
        return items.reduce(0.0) { total, item in
            if item.isDiscount {
                return total + abs(item.price)
            } else if let originalPrice = item.originalPrice, originalPrice > item.price {
                return total + (originalPrice - item.price)
            }
            return total
        }
    }

    init(id: UUID, user_id: UUID, image_urls: [String] = [], total_amount: Double, items: [ReceiptItem], store_name: String, store_address: String, receipt_name: String, purchase_date: Date, currency: String, payment_method: String, total_tax: Double, logo_search_term: String? = nil) {
        self.id = id
        self.user_id = user_id
        self.image_urls = image_urls
        self.total_amount = total_amount
        self.items = items
        self.store_name = store_name
        self.store_address = store_address
        self.receipt_name = receipt_name
        self.purchase_date = purchase_date
        self.currency = currency
        self.payment_method = payment_method
        self.total_tax = total_tax
        self.logo_search_term = logo_search_term
    }

    init(id: UUID, user_id: UUID, image_url: String, total_amount: Double, items: [ReceiptItem], store_name: String, store_address: String, receipt_name: String, purchase_date: Date, currency: String, payment_method: String, total_tax: Double, logo_search_term: String? = nil) {
        let urls = image_url != "placeholder_url" ? [image_url] : []
        self.init(id: id, user_id: user_id, image_urls: urls, total_amount: total_amount, items: items, store_name: store_name, store_address: store_address, receipt_name: receipt_name, purchase_date: purchase_date, currency: currency, payment_method: payment_method, total_tax: total_tax, logo_search_term: logo_search_term)
    }
}

struct ReceiptItem: Identifiable, Codable, Equatable {
    var id: UUID
    var name: String
    var price: Double
    var category: String
    var originalPrice: Double?
    var discountDescription: String?
    var isDiscount: Bool

    init(id: UUID, name: String, price: Double, category: String, originalPrice: Double? = nil, discountDescription: String? = nil, isDiscount: Bool = false) {
        self.id = id
        self.name = name
        self.price = price
        self.category = category
        self.originalPrice = originalPrice
        self.discountDescription = discountDescription
        self.isDiscount = isDiscount
    }
}

// MARK: - Local Storage Service (Shared with main app)

class LocalStorageService {
    static let shared = LocalStorageService()

    private let receiptsKey = "local_receipts"
    // Use App Groups for shared data between main app and widgets
    private let sharedDefaults = UserDefaults(suiteName: "group.com.spendsmart.shared") ?? UserDefaults.standard

    private init() {}

    func saveReceipts(_ receipts: [Receipt]) {
        do {
            let encoder = JSONEncoder()
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
            dateFormatter.locale = Locale(identifier: "en_US_POSIX")
            dateFormatter.timeZone = TimeZone(secondsFromGMT: 0)
            encoder.dateEncodingStrategy = .formatted(dateFormatter)

            let data = try encoder.encode(receipts)
            sharedDefaults.set(data, forKey: receiptsKey)
        } catch {
            print("❌ Error saving receipts to local storage: \(error.localizedDescription)")
        }
    }

    func getReceipts() -> [Receipt] {
        guard let data = sharedDefaults.data(forKey: receiptsKey) else {
            return []
        }

        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .custom { decoder in
                let container = try decoder.singleValueContainer()
                let dateString = try container.decode(String.self)
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
                formatter.locale = Locale(identifier: "en_US_POSIX")
                formatter.timeZone = TimeZone(secondsFromGMT: 0)
                if let date = formatter.date(from: dateString) {
                    return date
                }
                throw DecodingError.dataCorruptedError(
                    in: container,
                    debugDescription: "Cannot decode date: \(dateString)"
                )
            }

            let receipts = try decoder.decode([Receipt].self, from: data)
            return receipts
        } catch {
            print("❌ Error retrieving receipts from local storage: \(error.localizedDescription)")
            return []
        }
    }

    func addReceipt(_ receipt: Receipt) {
        var receipts = getReceipts()
        receipts.append(receipt)
        saveReceipts(receipts)
    }

    func deleteReceipt(withId id: UUID) {
        var receipts = getReceipts()
        receipts.removeAll { $0.id == id }
        saveReceipts(receipts)
    }

    func clearAllReceipts() {
        sharedDefaults.removeObject(forKey: receiptsKey)
    }
}

// MARK: - Currency Manager (Shared with main app)

class CurrencyManager: ObservableObject {
    static let shared = CurrencyManager()

    private let preferredCurrencyKey = "preferred_currency"
    private let recentCurrenciesKey = "recent_currencies"
    // Use App Groups for shared data between main app and widgets
    private let sharedDefaults = UserDefaults(suiteName: "group.com.spendsmart.shared") ?? UserDefaults.standard

    @Published var preferredCurrency: String {
        didSet {
            sharedDefaults.set(preferredCurrency, forKey: preferredCurrencyKey)
        }
    }

    private init() {
        self.preferredCurrency = sharedDefaults.string(forKey: preferredCurrencyKey) ?? "USD"
    }

    func formatAmount(_ amount: Double, currencyCode: String) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currencyCode
        formatter.maximumFractionDigits = 2
        formatter.minimumFractionDigits = 2

        return formatter.string(from: NSNumber(value: amount)) ?? "\(amount)"
    }

    func convertAmountSync(_ amount: Double, from sourceCurrency: String, to targetCurrency: String) -> Double {
        // For widgets, we'll use a simplified conversion
        // In a full implementation, this would use cached exchange rates
        if sourceCurrency == targetCurrency {
            return amount
        }

        // Simplified conversion rates (these should be cached from the main app)
        let exchangeRates: [String: Double] = [
            "USD": 1.0,
            "EUR": 0.85,
            "GBP": 0.73,
            "CAD": 1.25,
            "AUD": 1.35,
            "JPY": 110.0
        ]

        let usdAmount = amount / (exchangeRates[sourceCurrency] ?? 1.0)
        return usdAmount * (exchangeRates[targetCurrency] ?? 1.0)
    }
}

// MARK: - Font Extensions (Shared with main app)

extension Font {
    static func spaceGrotesk(size: CGFloat, weight: Font.Weight = .regular) -> Font {
        switch weight {
        case .medium:
            return Font.custom("SpaceGrotesk-Light_Medium", size: size)
        case .bold:
            return Font.custom("SpaceGrotesk-Light_Bold", size: size)
        default:
            return Font.custom("SpaceGrotesk-Light_Regular", size: size)
        }
    }

    static func instrumentSans(size: CGFloat, weight: Font.Weight = .regular) -> Font {
        switch weight {
        case .medium:
            return Font.custom("InstrumentSans-Regular_Medium", size: size)
        case .semibold:
            return Font.custom("InstrumentSans-Regular_SemiBold", size: size)
        case .bold:
            return Font.custom("InstrumentSans-Regular_Bold", size: size)
        default:
            return Font.custom("InstrumentSans-Regular", size: size)
        }
    }
}
