<div align="center">

  <img src="https://github.com/user-attachments/assets/a0819fb3-ffe6-458f-b1aa-134dcaa3491b" alt="SpendSmart Logo" width="150"/>

  <h1>SpendSmart 💰</h1>

  <p><strong>SpendSmart</strong> is an iOS expense tracking app with AI-powered receipt scanning and spending analytics.<br/>
  Snap receipts, track expenses, and visualize your spending patterns.</p>

  <a href="https://apps.apple.com/us/app/spendsmart-ai-receipt-tool/id6745190294?itscg=30200&itsct=apps_box_badge&mttnsubad=6745190294">
    <img src="https://toolbox.marketingtools.apple.com/api/v2/badges/download-on-the-app-store/black/en-us?releaseDate=1747180800" alt="Download on the App Store" width="200" />
  </a>
</div>

---

## 🚀 Features

- 📸 **AI Receipt Scanning** - Gemini 2.0 Flash extracts all receipt details automatically
- 📊 **Spending Analytics** - Interactive charts and category breakdowns
- 🗺️ **Spending Map (Beta)** - See your purchases on an interactive map
- ✏️ **Receipt Editing** - Manual editing with native iOS components
- 💱 **Multi-Currency** - Automatic currency conversion
- 🔄 **Smart Sync** - Local storage or cloud sync via Supabase
- 🔓 **Open Source** - 100% free and open source

---

## 📱 Screenshots

| Capture | Extract | Manage |
|--------|--------|--------|
| ![Capture](https://github.com/user-attachments/assets/458a19fa-2cc1-483e-abbd-2cd0cf75c99a) | ![Extract](https://github.com/user-attachments/assets/059bc146-7277-4a7f-a6c6-81d88544fb63) | ![Manage](https://github.com/user-attachments/assets/8b4a082a-ba40-46c9-b55c-b7477d0da197) |

> Additional App Store preview screenshots available [here](https://apple.co/43aJhQ5)

---

## 🛠 Tech Stack

- **iOS**: Swift + SwiftUI (iOS 17+)
- **AI**: Gemini 2.0 Flash
- **Storage**: Local + Supabase sync

---

## 📦 Installation

```bash
git clone https://github.com/yourusername/SpendSmart.git
cd SpendSmart
open SpendSmart.xcodeproj
```

### API Setup (Optional)
Create `APIKeys.swift` in project root:

```swift
let supabaseURL = "YOUR_SUPABASE_URL"
let supabaseAnonKey = "YOUR_SUPABASE_ANON_KEY"
let supabaseServiceRoleKey = "YOUR_SUPABASE_SERVICE_ROLE_KEY"
let geminiAPIKey = "YOUR_GEMINI_API_KEY"
let secretKey = "YOUR_SECRET_BACKEND_KEY"
let imgBBAPIKey = "YOUR_IMGBB_API_KEY"
```

Get keys from [Supabase](https://supabase.com/), [Google Cloud](https://console.cloud.google.com/), and [imgBB](https://api.imgbb.com/).

**Note**: App works in guest mode without API keys.

---

## ⭐️ Support

If you find SpendSmart useful, consider giving it a ⭐️!

---

## 👋 Contact

Built by **Shaurya Gupta**
- Twitter: [@madebyshaurya](https://twitter.com/madebyshaurya)
- GitHub: [@madebyshaurya](https://github.com/madebyshaurya)
