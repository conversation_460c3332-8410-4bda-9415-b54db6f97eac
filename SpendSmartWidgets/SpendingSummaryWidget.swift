//
//  SpendingSummaryWidget.swift
//  SpendSmartWidgets
//
//  Created by SpendSmart Team on 2025-01-20.
//

import WidgetKit
import SwiftUI
import Charts

struct SpendingSummaryWidget: Widget {
    let kind: String = "SpendingSummaryWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: SpendingSummaryProvider()) { entry in
            SpendingSummaryWidgetEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
        }
        .configurationDisplayName("Spending Summary")
        .description("View your current month spending and budget progress.")
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
    }
}

struct SpendingSummaryEntry: TimelineEntry {
    let date: Date
    let totalExpense: Double
    let totalSavings: Double
    let receiptCount: Int
    let budgetProgress: Double
    let estimatedBudget: Double
    let monthlyData: [(month: String, amount: Double)]
}

struct SpendingSummaryProvider: TimelineProvider {
    func placeholder(in context: Context) -> SpendingSummaryEntry {
        SpendingSummaryEntry(
            date: Date(),
            totalExpense: 1250.50,
            totalSavings: 45.25,
            receiptCount: 23,
            budgetProgress: 0.62,
            estimatedBudget: 2000.0,
            monthlyData: [
                ("Jan", 1200),
                ("Feb", 1350),
                ("Mar", 1100),
                ("Apr", 1450),
                ("May", 1250),
                ("Jun", 1300)
            ]
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (SpendingSummaryEntry) -> ()) {
        let entry = createEntry()
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        let entry = createEntry()

        // Update every hour
        let nextUpdate = Calendar.current.date(byAdding: .hour, value: 1, to: Date()) ?? Date()
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))

        completion(timeline)
    }

    private func createEntry() -> SpendingSummaryEntry {
        let dataService = WidgetDataService.shared
        let summary = dataService.calculateCurrentMonthSummary()
        let budgetProgress = dataService.getBudgetProgress()
        let estimatedBudget = dataService.getEstimatedMonthlyBudget()
        let monthlyData = dataService.getMonthlySpendingData()

        return SpendingSummaryEntry(
            date: Date(),
            totalExpense: summary.totalExpense,
            totalSavings: summary.totalSavings,
            receiptCount: summary.receiptCount,
            budgetProgress: budgetProgress,
            estimatedBudget: estimatedBudget,
            monthlyData: monthlyData
        )
    }
}

struct SpendingSummaryWidgetEntryView: View {
    var entry: SpendingSummaryProvider.Entry
    @Environment(\.widgetFamily) var family
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        switch family {
        case .systemSmall:
            SmallSpendingSummaryView(entry: entry)
        case .systemMedium:
            MediumSpendingSummaryView(entry: entry)
        case .systemLarge:
            LargeSpendingSummaryView(entry: entry)
        default:
            SmallSpendingSummaryView(entry: entry)
        }
    }
}

// MARK: - Small Widget View
struct SmallSpendingSummaryView: View {
    let entry: SpendingSummaryEntry
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        VStack(spacing: 8) {
            // Header
            HStack {
                Text("This Month")
                    .font(.instrumentSans(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
                Spacer()
                Image(systemName: "chart.bar.fill")
                    .font(.system(size: 12))
                    .foregroundColor(.blue)
            }

            // Main Amount
            VStack(spacing: 2) {
                Text(CurrencyManager.shared.formatAmount(entry.totalExpense, currencyCode: CurrencyManager.shared.preferredCurrency))
                    .font(.spaceGrotesk(size: 18, weight: .bold))
                    .foregroundColor(colorScheme == .dark ? .white : .black)
                    .minimumScaleFactor(0.7)
                    .lineLimit(1)

                Text("\(entry.receiptCount) receipts")
                    .font(.instrumentSans(size: 10))
                    .foregroundColor(.secondary)
            }

            // Budget Progress
            VStack(spacing: 4) {
                HStack {
                    Text("Budget")
                        .font(.instrumentSans(size: 10))
                        .foregroundColor(.secondary)
                    Spacer()
                    Text("\(Int(entry.budgetProgress * 100))%")
                        .font(.instrumentSans(size: 10, weight: .medium))
                        .foregroundColor(entry.budgetProgress > 0.8 ? .red : .blue)
                }

                ProgressView(value: entry.budgetProgress)
                    .progressViewStyle(LinearProgressViewStyle(tint: entry.budgetProgress > 0.8 ? .red : .blue))
                    .scaleEffect(y: 0.8)
            }

            // Savings (only show if > 0)
            if entry.totalSavings > 0 {
                HStack {
                    Image(systemName: "tag.fill")
                        .font(.system(size: 8))
                        .foregroundColor(.green)
                    Text("Saved \(CurrencyManager.shared.formatAmount(entry.totalSavings, currencyCode: CurrencyManager.shared.preferredCurrency))")
                        .font(.instrumentSans(size: 9, weight: .medium))
                        .foregroundColor(.green)
                        .minimumScaleFactor(0.8)
                        .lineLimit(1)
                }
            }
        }
        .padding(12)
    }
}

// MARK: - Medium Widget View
struct MediumSpendingSummaryView: View {
    let entry: SpendingSummaryEntry
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        HStack(spacing: 16) {
            // Left side - Summary
            VStack(alignment: .leading, spacing: 8) {
                Text("This Month")
                    .font(.instrumentSans(size: 14, weight: .medium))
                    .foregroundColor(.secondary)

                Text(CurrencyManager.shared.formatAmount(entry.totalExpense, currencyCode: CurrencyManager.shared.preferredCurrency))
                    .font(.spaceGrotesk(size: 24, weight: .bold))
                    .foregroundColor(colorScheme == .dark ? .white : .black)
                    .minimumScaleFactor(0.7)
                    .lineLimit(1)

                Text("\(entry.receiptCount) receipts")
                    .font(.instrumentSans(size: 12))
                    .foregroundColor(.secondary)

                // Budget Progress
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text("Budget Progress")
                            .font(.instrumentSans(size: 11))
                            .foregroundColor(.secondary)
                        Spacer()
                        Text("\(Int(entry.budgetProgress * 100))%")
                            .font(.instrumentSans(size: 11, weight: .medium))
                            .foregroundColor(entry.budgetProgress > 0.8 ? .red : .blue)
                    }

                    ProgressView(value: entry.budgetProgress)
                        .progressViewStyle(LinearProgressViewStyle(tint: entry.budgetProgress > 0.8 ? .red : .blue))
                }

                // Savings (only show if > 0)
                if entry.totalSavings > 0 {
                    HStack {
                        Image(systemName: "tag.fill")
                            .font(.system(size: 10))
                            .foregroundColor(.green)
                        Text("Saved \(CurrencyManager.shared.formatAmount(entry.totalSavings, currencyCode: CurrencyManager.shared.preferredCurrency))")
                            .font(.instrumentSans(size: 11, weight: .medium))
                            .foregroundColor(.green)
                            .minimumScaleFactor(0.8)
                            .lineLimit(1)
                    }
                }
            }

            // Right side - Mini Chart
            VStack(alignment: .trailing, spacing: 4) {
                Text("6 Month Trend")
                    .font(.instrumentSans(size: 10))
                    .foregroundColor(.secondary)

                if !entry.monthlyData.isEmpty {
                    Chart(entry.monthlyData, id: \.month) { item in
                        BarMark(
                            x: .value("Month", item.month),
                            y: .value("Amount", item.amount)
                        )
                        .cornerRadius(2)
                        .foregroundStyle(Color.blue.gradient)
                    }
                    .chartXAxis(.hidden)
                    .chartYAxis(.hidden)
                    .frame(height: 60)
                }
            }
            .frame(maxWidth: 100)
        }
        .padding(16)
    }
}

// MARK: - Large Widget View
struct LargeSpendingSummaryView: View {
    let entry: SpendingSummaryEntry
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        VStack(spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Monthly Spending Summary")
                        .font(.instrumentSans(size: 16, weight: .semibold))
                        .foregroundColor(colorScheme == .dark ? .white : .black)

                    Text("Current month overview")
                        .font(.instrumentSans(size: 12))
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "chart.bar.fill")
                    .font(.system(size: 16))
                    .foregroundColor(.blue)
            }

            // Main Stats Row
            HStack(spacing: 20) {
                // Total Spending
                VStack(alignment: .leading, spacing: 4) {
                    Text("Total Spent")
                        .font(.instrumentSans(size: 12))
                        .foregroundColor(.secondary)

                    Text(CurrencyManager.shared.formatAmount(entry.totalExpense, currencyCode: CurrencyManager.shared.preferredCurrency))
                        .font(.spaceGrotesk(size: 20, weight: .bold))
                        .foregroundColor(colorScheme == .dark ? .white : .black)
                        .minimumScaleFactor(0.7)
                        .lineLimit(1)
                }

                Spacer()

                // Receipt Count
                VStack(alignment: .center, spacing: 4) {
                    Text("Receipts")
                        .font(.instrumentSans(size: 12))
                        .foregroundColor(.secondary)

                    Text("\(entry.receiptCount)")
                        .font(.spaceGrotesk(size: 20, weight: .bold))
                        .foregroundColor(.blue)
                }

                Spacer()

                // Savings (only show if > 0)
                if entry.totalSavings > 0 {
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("Saved")
                            .font(.instrumentSans(size: 12))
                            .foregroundColor(.secondary)

                        Text(CurrencyManager.shared.formatAmount(entry.totalSavings, currencyCode: CurrencyManager.shared.preferredCurrency))
                            .font(.spaceGrotesk(size: 20, weight: .bold))
                            .foregroundColor(.green)
                            .minimumScaleFactor(0.7)
                            .lineLimit(1)
                    }
                }
            }

            // Budget Progress
            VStack(spacing: 8) {
                HStack {
                    Text("Budget Progress")
                        .font(.instrumentSans(size: 14, weight: .medium))
                        .foregroundColor(colorScheme == .dark ? .white : .black)

                    Spacer()

                    Text("\(Int(entry.budgetProgress * 100))% of \(CurrencyManager.shared.formatAmount(entry.estimatedBudget, currencyCode: CurrencyManager.shared.preferredCurrency))")
                        .font(.instrumentSans(size: 12))
                        .foregroundColor(.secondary)
                }

                ProgressView(value: entry.budgetProgress)
                    .progressViewStyle(LinearProgressViewStyle(tint: entry.budgetProgress > 0.8 ? .red : .blue))
                    .scaleEffect(y: 1.2)
            }

            // 6 Month Chart
            VStack(alignment: .leading, spacing: 8) {
                Text("6 Month Spending Trend")
                    .font(.instrumentSans(size: 14, weight: .medium))
                    .foregroundColor(colorScheme == .dark ? .white : .black)

                if !entry.monthlyData.isEmpty {
                    Chart(entry.monthlyData, id: \.month) { item in
                        BarMark(
                            x: .value("Month", item.month),
                            y: .value("Amount", item.amount)
                        )
                        .cornerRadius(4)
                        .foregroundStyle(Color.blue.gradient)
                    }
                    .chartYAxis {
                        AxisMarks(position: .leading) { value in
                            if let doubleValue = value.as(Double.self) {
                                AxisGridLine()
                                AxisValueLabel {
                                    Text(CurrencyManager.shared.formatAmount(doubleValue, currencyCode: CurrencyManager.shared.preferredCurrency))
                                        .font(.instrumentSans(size: 10))
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    }
                    .chartXAxis {
                        AxisMarks { value in
                            if let stringValue = value.as(String.self) {
                                AxisValueLabel {
                                    Text(stringValue)
                                        .font(.instrumentSans(size: 10))
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    }
                    .frame(height: 80)
                }
            }
        }
        .padding(16)
    }
}
